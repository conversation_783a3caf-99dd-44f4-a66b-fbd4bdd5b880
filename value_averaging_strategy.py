#encoding:gbk
"""
价值平均+择时量化投资策略
基于创业板ETF(159915)技术指标进行择时，在沉睡期持有红利国企ETF(510720)，激活期持有创业板ETF(159915)
使用价值平均策略进行仓位管理
"""

import sqlite3
import datetime
import json
import math
import time
from typing import Dict, List, Tuple, Optional

# ==================== 策略参数配置 ====================
# 所有可配置参数都在此处定义，方便修改

# 基金代码配置
SLEEPING_FUND_CODE = "510720.SZ"    # 沉睡期基金：国泰上证国有企业红利ETF
ACTIVE_FUND_CODE = "159915.SZ"      # 激活期基金：易方达创业板ETF
SIGNAL_FUND_CODE = "159915.SZ"      # 信号检测基金：创业板ETF

# 价值平均策略参数
PERIOD_INVESTMENT_AMOUNT = 10000  # 每期投入金额（元）
INVESTMENT_CYCLE = "1mon"         # 投资周期：60分钟、日、周、季、月
SLEEPING_POSITION_RATIO = 1.0    # 沉睡期基金仓位比例：默认100%

# 技术指标参数
EMA_DETECTION_CYCLE = "1q"      # EMA检测周期：60分钟线、日线、周线、季线、月线
EMA_PERIOD = 35                  # EMA参数：默认35，可配置2-500
BOTTOM_RATIO = 0.85             # 底部相对比例：默认0.85
TOP_RATIO = 1.90                # 顶部相对比例：默认1.90

# 信号过滤参数
BUY_SIGNAL_FILTER_PERIODS = 8   # 买入信号过滤周期：8个周期内不重复
SELL_SIGNAL_FILTER_PERIODS = 10 # 卖出信号过滤周期：10个周期内不重复

# 交易执行参数
MAX_RETRY_COUNT = 3             # 最大重试次数
RETRY_INTERVAL = 300            # 重试间隔（秒）
MIN_TRADE_SHARES = 100          # 最小交易股数（必须是100的倍数）

# 交易账户信息
ACCOUNT_ID = "************"                 # 设置交易账户信息（只能支持一个）

# 数据库配置
DATABASE_PATH = "gytrading2.db"   # 数据库文件路径

# ==================== 全局变量 ====================
g_strategy_status = None        # 策略状态
g_db_connection = None          # 数据库连接


def init(ContextInfo):
    """
    策略初始化函数
    在策略启动时调用一次，用于初始化数据库、加载策略状态等
    """
    try:
        print("=" * 60)
        print("价值平均+择时量化投资策略 - 初始化开始")
        print("=" * 60)

        # 1. 初始化数据库
        print("正在初始化数据库...")
        init_database()
        print("? 数据库初始化完成")

        # 2. 加载策略状态
        print("正在加载策略状态...")
        load_strategy_status()
        print("? 策略状态加载完成")

        # 3. 验证策略状态
        print("正在验证策略状态...")
        if validate_strategy_state():
            print("? 策略状态验证通过")
        else:
            print("? 策略状态验证失败，将重置状态")
            reset_strategy_state()

        # 4. 设置账户信息
        ContextInfo.set_account(ACCOUNT_ID)
        print(f"已设置交易账户:{ACCOUNT_ID}")

        # 5. 显示策略配置信息
        print("\n策略配置信息：")
        print(f"  沉睡期基金：{SLEEPING_FUND_CODE} (国泰上证国有企业红利ETF)")
        print(f"  激活期基金：{ACTIVE_FUND_CODE} (易方达创业板ETF)")
        print(f"  信号检测基金：{SIGNAL_FUND_CODE}")
        print(f"  每期投入金额：{PERIOD_INVESTMENT_AMOUNT:,}元")
        print(f"  投资周期：{INVESTMENT_CYCLE}")
        print(f"  EMA检测周期：{EMA_DETECTION_CYCLE}")
        print(f"  EMA参数：{EMA_PERIOD}")
        print(f"  底部比例：{BOTTOM_RATIO}")
        print(f"  顶部比例：{TOP_RATIO}")

        # 6. 显示当前策略状态
        print(f"\n当前策略状态：")
        print(f"  阶段：{g_strategy_status['current_phase']}")
        print(f"  最后检测时间：{g_strategy_status['last_check_time']}")
        if g_strategy_status['current_phase'] == 'active':
            print(f"  当前期数：{g_strategy_status['current_period']}")
            print(f"  起始期日期：{g_strategy_status['start_period_date']}")
            print(f"  起始期价格：{g_strategy_status['start_period_price']}")

        # 7. 获取策略表现摘要
        performance = get_strategy_performance_summary()
        print(f"\n历史表现摘要：")
        print(f"  总交易次数：{performance['total_trades']}")
        print(f"  成功交易：{performance['successful_trades']}")
        print(f"  失败交易：{performance['failed_trades']}")
        print(f"  总信号数：{performance['total_signals']}")
        print(f"  有效信号：{performance['valid_signals']}")
        print(f"  过滤信号：{performance['filtered_signals']}")

        # 此处为测试版 TODO 改进到另外一个策略定时跑
        print(f'即将下载 {SIGNAL_FUND_CODE} 的日线数据...')
        down_history_data(SIGNAL_FUND_CODE, '1d', '19900101', '20250730')
        print(f'{SIGNAL_FUND_CODE} 的日线数据下载完成！')

        # 8. 记录初始化日志
        log_message("INFO", "策略初始化", "策略初始化成功")

        print("\n" + "=" * 60)
        print("价值平均+择时策略初始化完成！")
        print("=" * 60)

    except Exception as e:
        error_msg = f"策略初始化失败：{str(e)}"
        print(f"\n? {error_msg}")
        print("=" * 60)
        log_message("ERROR", "策略初始化", error_msg)
        raise e  # 重新抛出异常，确保初始化失败时策略不会运行


def handlebar(ContextInfo):
    """
    主策略逻辑函数
    在每根K线结束时调用，执行核心策略逻辑
    """
    try:
        # 只在最后一根K线时执行策略逻辑
        if not ContextInfo.is_last_bar():
            return
        
        # 获取当前时间
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"当前为最后一根K线，执行策略，时间：{current_time}")
        
        # 验证策略状态
        if not validate_strategy_state():
            log_message("ERROR", "策略运行", "策略状态验证失败，跳过本次执行")
            return

        # 检查数据库连接
        if g_db_connection is None:
            log_message("ERROR", "策略运行", "数据库连接丢失，跳过本次执行")
            return

        # 1. 更新技术指标
        try:
            technical_data = update_technical_indicators(ContextInfo)
            #print('更新技术指标 ===> ')
            #print(technical_data)
            if technical_data is None:
                log_message("WARNING", "策略运行", "技术指标更新失败，跳过本次执行")
                return
        except Exception as e:
            log_message("ERROR", "策略运行", f"技术指标更新异常：{str(e)}")
            return

        # 2. 执行信号检测
        try:
            signal_result = detect_signals(ContextInfo)
            print('执行信号检测==>')
            print(signal_result)
            if signal_result is None:
                log_message("WARNING", "策略运行", "信号检测失败，跳过本次执行")
                return
        except Exception as e:
            log_message("ERROR", "策略运行", f"信号检测异常：{str(e)}")
            return

        # 3. 记录信号检测结果
        if signal_result.get('has_buy_signal'):
            log_message("INFO", "策略运行", "检测到买入信号")
        if signal_result.get('has_sell_signal'):
            log_message("INFO", "策略运行", "检测到卖出信号")

        # 4. 执行交易逻辑
        try:
            execute_trading_logic(ContextInfo, signal_result)
        except Exception as e:
            log_message("ERROR", "策略运行", f"交易逻辑执行异常：{str(e)}")
            # 交易失败不应该中断策略运行，继续执行状态更新

        # 5. 更新策略状态
        try:
            update_strategy_status(current_time)
        except Exception as e:
            log_message("ERROR", "策略运行", f"策略状态更新异常：{str(e)}")

        # 6. 定期输出策略状态（每10次执行输出一次详细信息）
        if hasattr(ContextInfo, 'run_count'):
            ContextInfo.run_count += 1
        else:
            ContextInfo.run_count = 1

        if ContextInfo.run_count % 10 == 0:
            performance = get_strategy_performance_summary()
            log_message("INFO", "策略运行",
                       f"策略运行统计 - 阶段：{performance['current_phase']}, "
                       f"交易：{performance['successful_trades']}/{performance['total_trades']}, "
                       f"信号：{performance['valid_signals']}/{performance['total_signals']}")

        # 7. 记录运行日志
        log_message("INFO", "策略运行",
                   f"策略执行完成，当前状态：{g_strategy_status['current_phase']}, "
                   f"运行次数：{ContextInfo.run_count}")

    except Exception as e:
        error_msg = f"策略执行失败：{str(e)}"
        print(f"? {error_msg}")
        log_message("ERROR", "策略执行", error_msg)

        # 记录跳过周期
        try:
            record_skip_period(error_msg)
        except:
            pass  # 避免二次异常


def record_skip_period(reason: str):
    """
    记录跳过的周期

    Args:
        reason: 跳过原因
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取当前期数
        current_period = g_strategy_status.get('current_period', 0) if g_strategy_status else 0

        cursor.execute("""
            INSERT INTO skip_periods
            (skip_date, period_number, target_amount, available_funds,
             required_funds, skip_reason, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            current_time, current_period, 0, 0, 0, reason, current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录跳过周期失败：{str(e)}")


# ==================== 异常处理和重试机制 ====================

def retry_on_failure(func, max_retries: int = MAX_RETRY_COUNT, delay: int = RETRY_INTERVAL):
    """
    通用重试装饰器函数

    Args:
        func: 要重试的函数
        max_retries: 最大重试次数
        delay: 重试间隔（秒）

    Returns:
        函数执行结果
    """
    def wrapper(*args, **kwargs):
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    log_message("WARNING", "重试机制",
                               f"函数{func.__name__}执行失败，第{attempt + 1}次重试：{str(e)}")
                    time.sleep(delay)
                else:
                    log_message("ERROR", "重试机制",
                               f"函数{func.__name__}重试{max_retries}次后仍然失败：{str(e)}")

        # 所有重试都失败，抛出最后一个异常
        raise last_exception

    return wrapper


def safe_execute(func, *args, default_return=None, log_prefix="", **kwargs):
    """
    安全执行函数，捕获异常并返回默认值

    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 异常时的默认返回值
        log_prefix: 日志前缀
        **kwargs: 函数关键字参数

    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_msg = f"{log_prefix}执行失败：{str(e)}"
        log_message("ERROR", "安全执行", error_msg)
        return default_return


def validate_trading_environment(ContextInfo) -> bool:
    """
    验证交易环境是否正常

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 环境是否正常
    """
    try:
        # 检查数据库连接
        if g_db_connection is None:
            log_message("ERROR", "环境验证", "数据库连接为空")
            return False

        # 检查策略状态
        if g_strategy_status is None:
            log_message("ERROR", "环境验证", "策略状态为空")
            return False

        # 检查账户ID
        if not hasattr(ContextInfo, 'accid') or not ContextInfo.accid:
            log_message("ERROR", "环境验证", "账户ID未设置")
            return False

        # 检查是否为交易时间（简化检查）
        current_time = datetime.datetime.now()
        if current_time.weekday() >= 5:  # 周末
            log_message("WARNING", "环境验证", "当前为周末，非交易时间")
            return False

        # 检查基本的市场数据获取能力
        try:
            test_data = ContextInfo.get_market_data_ex(
                fields=['close'],
                stock_code=[SIGNAL_FUND_CODE],
                period='1min',
                count=1,
                dividend_type='front',
                fill_data=True
            )
            if test_data is None or len(test_data) == 0:
                log_message("WARNING", "环境验证", "无法获取市场数据")
                return False

            # 检查是否有具体股票的数据
            stock_data = test_data.get(SIGNAL_FUND_CODE)
            if stock_data is None or len(stock_data) == 0:
                log_message("WARNING", "环境验证", "无法获取市场数据")
                return False
        except Exception as e:
            log_message("ERROR", "环境验证", f"市场数据获取测试失败：{str(e)}")
            return False

        return True

    except Exception as e:
        log_message("ERROR", "环境验证", f"交易环境验证失败：{str(e)}")
        return False


def handle_critical_error(error_msg: str, ContextInfo=None):
    """
    处理关键错误

    Args:
        error_msg: 错误消息
        ContextInfo: iQuant上下文信息对象
    """
    try:
        # 记录关键错误
        log_message("CRITICAL", "关键错误", error_msg)

        # 发送错误通知（如果有通知机制）
        print(f"?? 关键错误：{error_msg}")

        # 保存当前状态
        if g_strategy_status and g_db_connection:
            try:
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                update_strategy_status(current_time)
            except:
                pass

        # 记录错误到跳过周期表
        try:
            record_skip_period(f"关键错误：{error_msg}")
        except:
            pass

    except Exception as e:
        print(f"处理关键错误时发生异常：{str(e)}")


def cleanup_resources():
    """
    清理资源
    在策略停止时调用
    """
    try:
        global g_db_connection

        if g_db_connection:
            # 记录策略停止日志
            log_message("INFO", "资源清理", "策略停止，开始清理资源")

            # 关闭数据库连接
            g_db_connection.close()
            g_db_connection = None

            print("? 资源清理完成")

    except Exception as e:
        print(f"资源清理失败：{str(e)}")


def get_error_statistics() -> Dict:
    """
    获取错误统计信息

    Returns:
        dict: 错误统计
    """
    try:
        stats = {
            'total_errors': 0,
            'critical_errors': 0,
            'warning_count': 0,
            'retry_count': 0,
            'skip_periods': 0
        }

        if g_db_connection is None:
            return stats

        cursor = g_db_connection.cursor()

        # 统计日志中的错误
        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE log_type = 'ERROR'")
        result = cursor.fetchone()
        if result:
            stats['total_errors'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE log_type = 'CRITICAL'")
        result = cursor.fetchone()
        if result:
            stats['critical_errors'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE log_type = 'WARNING'")
        result = cursor.fetchone()
        if result:
            stats['warning_count'] = result[0]

        # 统计重试次数
        cursor.execute("SELECT SUM(retry_count) FROM trade_orders WHERE retry_count > 0")
        result = cursor.fetchone()
        if result and result[0]:
            stats['retry_count'] = result[0]

        # 统计跳过周期
        cursor.execute("SELECT COUNT(*) FROM skip_periods")
        result = cursor.fetchone()
        if result:
            stats['skip_periods'] = result[0]

        return stats

    except Exception as e:
        log_message("ERROR", "错误统计", f"获取错误统计失败：{str(e)}")
        return {
            'total_errors': 0,
            'critical_errors': 0,
            'warning_count': 0,
            'retry_count': 0,
            'skip_periods': 0
        }


def init_database():
    """
    初始化SQLite数据库
    创建所有必要的表结构和索引
    """
    global g_db_connection

    try:
        # 连接数据库
        g_db_connection = sqlite3.connect(DATABASE_PATH)
        cursor = g_db_connection.cursor()

        # 创建策略状态表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                current_phase TEXT NOT NULL,           -- 当前阶段：'sleeping' 或 'active'
                last_check_time TEXT NOT NULL,         -- 最后检测时间
                first_activation_time TEXT,            -- 首次激活时间
                start_period_date TEXT,                -- 价值平均起始期日期（最高点日期）
                start_period_price REAL,              -- 起始期价格
                current_period INTEGER DEFAULT 0,      -- 当前期数
                created_time TEXT NOT NULL,           -- 创建时间
                updated_time TEXT NOT NULL            -- 更新时间
            )
        """)

        # 创建信号历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS signal_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_date TEXT NOT NULL,            -- 信号日期
                signal_type TEXT NOT NULL,            -- 信号类型：'ENTERLONG' 或 'EXITLONG'
                signal_price REAL NOT NULL,           -- 信号价格
                ema_value REAL NOT NULL,              -- EMA值
                bottom_line REAL,                     -- 底部线F1值（买入信号时）
                top_line REAL,                        -- 顶部线F2值（卖出信号时）
                is_valid INTEGER NOT NULL,            -- 是否有效信号：1有效，0无效
                filter_reason TEXT,                   -- 过滤原因
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建交易指令表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_date TEXT NOT NULL,             -- 下单日期
                stock_code TEXT NOT NULL,             -- 股票代码
                order_type TEXT NOT NULL,             -- 订单类型：'BUY' 或 'SELL'
                order_reason TEXT NOT NULL,           -- 下单原因：'SIGNAL_BUY', 'SIGNAL_SELL', 'VALUE_AVERAGE'
                target_amount REAL,                   -- 目标金额
                target_shares INTEGER,                -- 目标股数
                actual_shares INTEGER,                -- 实际成交股数
                actual_price REAL,                    -- 实际成交价格
                order_status TEXT NOT NULL,           -- 订单状态：'PENDING', 'SUCCESS', 'FAILED', 'RETRY'
                retry_count INTEGER DEFAULT 0,        -- 重试次数
                error_message TEXT,                   -- 错误信息
                execution_time TEXT,                  -- 执行时间
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建持仓记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS position_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                record_date TEXT NOT NULL,            -- 记录日期
                stock_code TEXT NOT NULL,             -- 股票代码
                shares INTEGER NOT NULL,              -- 持仓股数
                avg_cost REAL NOT NULL,               -- 平均成本
                market_value REAL NOT NULL,           -- 市值
                current_price REAL NOT NULL,          -- 当前价格
                period_number INTEGER,                -- 期数（仅159915有效）
                target_value REAL,                    -- 目标价值（仅159915有效）
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建跳过周期表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS skip_periods (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                skip_date TEXT NOT NULL,              -- 跳过日期
                period_number INTEGER NOT NULL,       -- 跳过的期数
                target_amount REAL NOT NULL,          -- 目标金额
                available_funds REAL NOT NULL,        -- 可用资金
                required_funds REAL NOT NULL,         -- 需要资金
                skip_reason TEXT NOT NULL,            -- 跳过原因
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建交易执行日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,               -- 日志日期
                log_type TEXT NOT NULL,               -- 日志类型：'INFO', 'WARNING', 'ERROR'
                operation TEXT NOT NULL,              -- 操作类型
                message TEXT NOT NULL,                -- 日志消息
                details TEXT,                         -- 详细信息（JSON格式）
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建账户信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id TEXT NOT NULL,             -- 账户ID
                total_assets REAL NOT NULL,           -- 总资产
                available_cash REAL NOT NULL,         -- 可用现金
                credit_limit REAL,                    -- 融资额度
                credit_available REAL,                -- 可用融资
                update_time TEXT NOT NULL,            -- 更新时间
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建索引
        create_database_indexes(cursor)

        # 检查是否需要插入初始策略状态
        cursor.execute("SELECT COUNT(*) FROM strategy_status")
        if cursor.fetchone()[0] == 0:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cursor.execute("""
                INSERT INTO strategy_status
                (current_phase, last_check_time, created_time, updated_time)
                VALUES ('sleeping', ?, ?, ?)
            """, (current_time, current_time, current_time))

        g_db_connection.commit()
        print("数据库初始化完成")

    except Exception as e:
        error_msg = f"数据库初始化失败：{str(e)}"
        print(error_msg)
        raise e


def create_database_indexes(cursor):
    """
    创建数据库索引
    提高查询性能
    """
    try:
        # 信号历史表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_date ON signal_history(signal_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_type ON signal_history(signal_type)")

        # 交易指令表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_date ON trade_orders(order_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_status ON trade_orders(order_status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_code ON trade_orders(stock_code)")

        # 持仓记录表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_position_records_date ON position_records(record_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_position_records_code ON position_records(stock_code)")

        # 跳过周期表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_skip_periods_date ON skip_periods(skip_date)")

        # 交易日志表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_logs_date ON trade_logs(log_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_logs_type ON trade_logs(log_type)")

    except Exception as e:
        print(f"创建索引失败：{str(e)}")


def load_strategy_status():
    """
    加载策略状态
    从数据库中读取当前策略状态信息
    """
    global g_strategy_status

    try:
        cursor = g_db_connection.cursor()
        cursor.execute("SELECT * FROM strategy_status ORDER BY id DESC LIMIT 1")
        result = cursor.fetchone()

        if result:
            g_strategy_status = {
                'id': result[0],
                'current_phase': result[1],
                'last_check_time': result[2],
                'first_activation_time': result[3],
                'start_period_date': result[4],
                'start_period_price': result[5],
                'current_period': result[6],
                'created_time': result[7],
                'updated_time': result[8]
            }
        else:
            # 如果没有记录，创建默认状态
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            g_strategy_status = {
                'id': None,
                'current_phase': 'sleeping',
                'last_check_time': current_time,
                'first_activation_time': None,
                'start_period_date': None,
                'start_period_price': None,
                'current_period': 0,
                'created_time': current_time,
                'updated_time': current_time
            }

    except Exception as e:
        error_msg = f"加载策略状态失败：{str(e)}"
        print(error_msg)
        raise e


def log_message(log_type: str, operation: str, message: str, details: Dict = None):
    """
    记录日志消息到数据库

    Args:
        log_type: 日志类型 ('INFO', 'WARNING', 'ERROR')
        operation: 操作类型
        message: 日志消息
        details: 详细信息字典
    """
    try:
        if g_db_connection is None:
            print(f"[{log_type}] {operation}: {message}")
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        details_json = json.dumps(details, ensure_ascii=False) if details else None

        cursor.execute("""
            INSERT INTO trade_logs (log_date, log_type, operation, message, details, created_time)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (current_time, log_type, operation, message, details_json, current_time))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录日志失败：{str(e)}")


def update_strategy_status(current_time: str):
    """
    更新策略状态到数据库

    Args:
        current_time: 当前时间字符串
    """
    try:
        if g_strategy_status is None:
            return

        cursor = g_db_connection.cursor()
        g_strategy_status['last_check_time'] = current_time
        g_strategy_status['updated_time'] = current_time

        if g_strategy_status['id'] is None:
            # 插入新记录
            cursor.execute("""
                INSERT INTO strategy_status
                (current_phase, last_check_time, first_activation_time, start_period_date,
                 start_period_price, current_period, created_time, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                g_strategy_status['current_phase'],
                g_strategy_status['last_check_time'],
                g_strategy_status['first_activation_time'],
                g_strategy_status['start_period_date'],
                g_strategy_status['start_period_price'],
                g_strategy_status['current_period'],
                g_strategy_status['created_time'],
                g_strategy_status['updated_time']
            ))
            g_strategy_status['id'] = cursor.lastrowid
        else:
            # 更新现有记录
            cursor.execute("""
                UPDATE strategy_status SET
                current_phase = ?, last_check_time = ?, first_activation_time = ?,
                start_period_date = ?, start_period_price = ?, current_period = ?,
                updated_time = ?
                WHERE id = ?
            """, (
                g_strategy_status['current_phase'],
                g_strategy_status['last_check_time'],
                g_strategy_status['first_activation_time'],
                g_strategy_status['start_period_date'],
                g_strategy_status['start_period_price'],
                g_strategy_status['current_period'],
                g_strategy_status['updated_time'],
                g_strategy_status['id']
            ))

        g_db_connection.commit()

    except Exception as e:
        error_msg = f"更新策略状态失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "策略状态更新", error_msg)


# ==================== 待实现的核心功能函数 ====================
# 以下函数将在后续任务中实现

def update_technical_indicators(ContextInfo):
    """
    更新技术指标
    获取159915的季线数据，计算EMA指标

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        # 获取创业板ETF的日线数据，然后重采样为季线数据用于计算EMA指标
        # 需要足够的历史数据来计算EMA，季线需要更多日线数据
        # 季线一年4个周期，所以需要的日线数据约为 required_quarters * 63 (一季度约63个交易日)
        required_quarters = max(EMA_PERIOD * 3, 100)
        required_daily_bars = required_quarters * 63  # 一季度约63个交易日

        # 使用get_market_data_ex获取日线数据
        market_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close'],
            stock_code=[SIGNAL_FUND_CODE],
            period='1d',  # 改为日线
            count=required_daily_bars,
            dividend_type='front',  # 前复权
            fill_data=True
        )
        
        #print("update_technical_indicators->market_data ==> ")
        #print(market_data)

        if market_data is None or len(market_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据")
            return None

        # 获取具体股票的数据
        stock_data = market_data.get(SIGNAL_FUND_CODE)
        if stock_data is None or len(stock_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据")
            return None
        
        print('日线数据打印测试 ===>')
        print(stock_data)
        
        # 将日线数据重采样为季线数据
        try:
            quarterly_data = resample_daily_to_period(stock_data, EMA_DETECTION_CYCLE)
            if quarterly_data is None or len(quarterly_data) == 0:
                log_message("WARNING", "技术指标更新", f"无法重采样{SIGNAL_FUND_CODE}的季线数据")
                return None
        except Exception as e:
            log_message("ERROR", "技术指标更新", f"重采样失败: {str(e)}")
            return None

        print('季度线数据打印测试 ===>')
        print(quarterly_data)
        
        # 计算EMA指标
        close_prices = quarterly_data['close'].values
        ema_values = calculate_ema(close_prices, EMA_PERIOD)
        print('ema values ==> ')
        print(ema_values)
        
        if len(ema_values) == 0:
            log_message("WARNING", "技术指标更新", "EMA计算结果为空")
            return None

        # 获取最新的EMA值
        current_ema = ema_values[-1]
        current_close = close_prices[-1]
        current_high = quarterly_data['high'].iloc[-1]

        # 计算底部线和顶部线
        bottom_line = current_ema * BOTTOM_RATIO  # F1 = EMA * 0.85
        top_line = current_ema * TOP_RATIO        # F2 = EMA * 1.90

        # 存储技术指标到全局变量或数据库
        technical_data = {
            'ema_value': current_ema,
            'bottom_line': bottom_line,
            'top_line': top_line,
            'current_close': current_close,
            'current_high': current_high,
            'update_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # 记录技术指标更新日志
        log_message("INFO", "技术指标更新",
                   f"EMA={current_ema:.4f}, 底部线={bottom_line:.4f}, 顶部线={top_line:.4f}, 收盘价={current_close:.4f}")

        return technical_data

    except Exception as e:
        error_msg = f"技术指标更新失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "技术指标更新", error_msg)
        return None


def resample_daily_to_period(daily_data, period_type='1q'):
    """
    将日线数据重采样为指定周期的数据

    Args:
        daily_data: 日线数据，包含 open, high, low, close 字段
        period_type: 周期类型，'1q'=季线, '1mon'=月线

    Returns:
        重采样后的数据
    """
    import pandas as pd

    print(f"[重采样调试] 开始重采样，目标周期: {period_type}")

    try:
        # 如果数据为空，返回None
        if daily_data is None:
            print("[重采样调试] 输入数据为None")
            return None

        print(f"[重采样调试] 输入数据类型: {type(daily_data)}")
        print(f"[重采样调试] 输入数据形状: {daily_data.shape if hasattr(daily_data, 'shape') else '无shape属性'}")

        # 将数据转换为DataFrame（如果还不是的话）
        if not isinstance(daily_data, pd.DataFrame):
            print(f"[重采样调试] 数据不是DataFrame，尝试转换...")
            if isinstance(daily_data, dict):
                daily_data = pd.DataFrame(daily_data)
                print(f"[重采样调试] 从字典转换为DataFrame成功，形状: {daily_data.shape}")
            else:
                print(f"[重采样调试] 无法转换数据类型，返回原数据")
                return daily_data

        # 确保数据有足够的行数
        if len(daily_data) == 0:
            print("[重采样调试] 数据为空，返回原数据")
            return daily_data

        print(f"[重采样调试] 数据列: {list(daily_data.columns)}")
        print(f"[重采样调试] 当前索引类型: {type(daily_data.index)}")
        print(f"[重采样调试] 索引前5个值: {daily_data.index[:5].tolist()}")

        # 如果没有时间索引，尝试创建一个简单的日期索引
        if not isinstance(daily_data.index, pd.DatetimeIndex):
            print("[重采样调试] 检测到非时间索引，创建时间索引...")
            # 创建一个简单的日期范围作为索引
            end_date = pd.Timestamp.now().normalize()  # 标准化到日期
            start_date = end_date - pd.Timedelta(days=len(daily_data)-1)
            new_index = pd.date_range(start=start_date, end=end_date, periods=len(daily_data))
            daily_data.index = new_index
            print(f"[重采样调试] 已创建时间索引: {daily_data.index[0]} 到 {daily_data.index[-1]}")

        # 根据周期类型设置重采样规则
        if period_type == '1q':
            rule = 'Q'  # 季度末
        elif period_type == '1mon':
            rule = 'M'  # 月末
        else:
            print(f"[重采样调试] 不支持的周期类型: {period_type}，返回原数据")
            return daily_data

        print(f"[重采样调试] 使用重采样规则: {rule}")

        # 准备重采样的聚合规则
        agg_rules = {}
        for col in daily_data.columns:
            if col in ['open']:
                agg_rules[col] = 'first'
            elif col in ['high']:
                agg_rules[col] = 'max'
            elif col in ['low']:
                agg_rules[col] = 'min'
            elif col in ['close']:
                agg_rules[col] = 'last'
            elif col in ['volume', 'amount']:
                agg_rules[col] = 'sum'
            else:
                agg_rules[col] = 'last'  # 默认取最后一个值

        print(f"[重采样调试] 聚合规则: {agg_rules}")

        # 执行重采样
        print("[重采样调试] 开始执行重采样...")
        resampled = daily_data.resample(rule).agg(agg_rules).dropna()

        print(f"[重采样调试] 重采样完成！")
        print(f"[重采样调试] 原始数据: {len(daily_data)} 行")
        print(f"[重采样调试] 重采样后: {len(resampled)} 行")
        print(f"[重采样调试] 重采样后索引: {resampled.index.tolist()}")

        if len(resampled) == 0:
            print("[重采样调试] 警告：重采样后数据为空！")
            return None

        return resampled

    except Exception as e:
        error_msg = f"重采样失败：{str(e)}"
        print(f"[重采样调试] 错误: {error_msg}")
        log_message("ERROR", "数据重采样", error_msg)
        # 重要：不要返回原数据，而是返回None或抛出异常
        raise Exception(f"数据重采样失败: {str(e)}")


def calculate_ema(prices: List[float], period: int) -> List[float]:
    """
    计算指数移动平均线(EMA)
    使用标准的EMA计算公式：EMA = (当前价格 * 2/(period+1)) + (前一日EMA * (period-1)/(period+1))

    Args:
        prices: 价格序列
        period: EMA周期

    Returns:
        List[float]: EMA值序列
    """
    if len(prices) < period:
        return []

    ema_values = []
    multiplier = 2.0 / (period + 1)

    # 第一个EMA值使用简单移动平均
    sma = sum(prices[:period]) / period
    ema_values.append(sma)

    # 计算后续的EMA值
    for i in range(period, len(prices)):
        ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
        ema_values.append(ema)

    return ema_values


def detect_signals(ContextInfo):
    """
    检测买入卖出信号
    基于EMA指标检测ENTERLONG和EXITLONG信号，并进行过滤验证

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 信号检测结果
    """
    try:
        # 获取技术指标数据
        technical_data = update_technical_indicators(ContextInfo)
        if technical_data is None:
            return {
                'has_buy_signal': False,
                'has_sell_signal': False,
                'signal_details': None
            }

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ema_value = technical_data['ema_value']
        bottom_line = technical_data['bottom_line']
        top_line = technical_data['top_line']
        current_close = technical_data['current_close']
        current_high = technical_data['current_high']

        # 获取前一期的数据用于判断穿越
        previous_data = get_previous_period_data(ContextInfo)
        if previous_data is None:
            log_message("WARNING", "信号检测", "无法获取前一期数据，跳过信号检测")
            return {
                'has_buy_signal': False,
                'has_sell_signal': False,
                'signal_details': None
            }

        # 检测买入信号：ENTERLONG = FILTER(CROSS(F1,C),8)
        # 即收盘价向下跌破底部线F1时触发
        has_buy_signal = False
        buy_signal_details = None

        if (previous_data['close'] >= previous_data['bottom_line'] and
            current_close < bottom_line):
            # 发生了向下穿越，检查信号过滤
            is_valid, filter_reason = check_signal_filter('ENTERLONG', current_time)
            if is_valid:
                has_buy_signal = True
                buy_signal_details = {
                    'signal_type': 'ENTERLONG',
                    'signal_price': current_close,
                    'ema_value': ema_value,
                    'bottom_line': bottom_line,
                    'top_line': None,
                    'signal_time': current_time
                }

                # 记录信号到数据库
                record_signal_to_db(buy_signal_details, True, None)
                log_message("INFO", "信号检测", f"检测到买入信号：收盘价{current_close:.4f}跌破底部线{bottom_line:.4f}")
            else:
                # 记录被过滤的信号
                filtered_signal = {
                    'signal_type': 'ENTERLONG',
                    'signal_price': current_close,
                    'ema_value': ema_value,
                    'bottom_line': bottom_line,
                    'top_line': None,
                    'signal_time': current_time
                }
                record_signal_to_db(filtered_signal, False, filter_reason)
                log_message("INFO", "信号检测", f"买入信号被过滤：{filter_reason}")

        # 检测卖出信号：EXITLONG = FILTER(CROSS(H,F2),10)
        # 即最高价向上突破顶部线F2时触发
        has_sell_signal = False
        sell_signal_details = None

        if (previous_data['high'] <= previous_data['top_line'] and
            current_high > top_line):
            # 发生了向上穿越，检查信号过滤
            is_valid, filter_reason = check_signal_filter('EXITLONG', current_time)
            if is_valid:
                has_sell_signal = True
                sell_signal_details = {
                    'signal_type': 'EXITLONG',
                    'signal_price': current_high,
                    'ema_value': ema_value,
                    'bottom_line': None,
                    'top_line': top_line,
                    'signal_time': current_time
                }

                # 记录信号到数据库
                record_signal_to_db(sell_signal_details, True, None)
                log_message("INFO", "信号检测", f"检测到卖出信号：最高价{current_high:.4f}突破顶部线{top_line:.4f}")
            else:
                # 记录被过滤的信号
                filtered_signal = {
                    'signal_type': 'EXITLONG',
                    'signal_price': current_high,
                    'ema_value': ema_value,
                    'bottom_line': None,
                    'top_line': top_line,
                    'signal_time': current_time
                }
                record_signal_to_db(filtered_signal, False, filter_reason)
                log_message("INFO", "信号检测", f"卖出信号被过滤：{filter_reason}")

        return {
            'has_buy_signal': has_buy_signal,
            'has_sell_signal': has_sell_signal,
            'buy_signal_details': buy_signal_details,
            'sell_signal_details': sell_signal_details
        }

    except Exception as e:
        error_msg = f"信号检测失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "信号检测", error_msg)
        return {
            'has_buy_signal': False,
            'has_sell_signal': False,
            'signal_details': None
        }


def get_previous_period_data(ContextInfo):
    """
    获取前一期的技术指标数据
    用于判断信号穿越

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 前一期数据
    """
    try:
        # 获取日线数据，然后重采样为季线数据
        required_quarters = max(EMA_PERIOD + 2, 50)
        required_daily_bars = required_quarters * 63  # 一季度约63个交易日

        market_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close'],
            stock_code=[SIGNAL_FUND_CODE],
            period='1d',  # 改为日线
            count=required_daily_bars,
            dividend_type='front',
            fill_data=True
        )

        if market_data is None or len(market_data) == 0:
            return None

        # 获取具体股票的数据
        stock_data = market_data.get(SIGNAL_FUND_CODE)
        if stock_data is None or len(stock_data) < 2:
            return None

        # 将日线数据重采样为季线数据
        try:
            quarterly_data = resample_daily_to_period(stock_data, '1q')
            if quarterly_data is None or len(quarterly_data) < 2:
                return None
        except Exception as e:
            log_message("ERROR", "前期数据获取", f"重采样失败: {str(e)}")
            return None

        # 计算前一期的EMA和线位
        close_prices = quarterly_data['close'].values
        ema_values = calculate_ema(close_prices, EMA_PERIOD)

        if len(ema_values) < 2:
            return None

        # 前一期的数据（倒数第二个）
        previous_ema = ema_values[-2]
        previous_close = close_prices[-2]
        previous_high = quarterly_data['high'].iloc[-2]
        previous_bottom_line = previous_ema * BOTTOM_RATIO
        previous_top_line = previous_ema * TOP_RATIO

        return {
            'ema': previous_ema,
            'close': previous_close,
            'high': previous_high,
            'bottom_line': previous_bottom_line,
            'top_line': previous_top_line
        }

    except Exception as e:
        log_message("ERROR", "获取前期数据", f"获取前一期数据失败：{str(e)}")
        return None


def record_signal_to_db(signal_details: Dict, is_valid: bool, filter_reason: str = None):
    """
    记录信号到数据库

    Args:
        signal_details: 信号详情
        is_valid: 是否有效信号
        filter_reason: 过滤原因
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            INSERT INTO signal_history
            (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
             is_valid, filter_reason, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            signal_details['signal_time'],
            signal_details['signal_type'],
            signal_details['signal_price'],
            signal_details['ema_value'],
            signal_details.get('bottom_line'),
            signal_details.get('top_line'),
            1 if is_valid else 0,
            filter_reason,
            current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "信号记录", f"记录信号到数据库失败：{str(e)}")


def execute_trading_logic(ContextInfo, signal_result):
    """
    执行交易逻辑
    根据当前策略状态和信号结果执行相应的交易操作

    Args:
        ContextInfo: iQuant上下文信息对象
        signal_result: 信号检测结果
    """
    try:
        current_phase = g_strategy_status['current_phase']

        # 处理信号触发的状态切换
        if signal_result['has_buy_signal'] and current_phase == 'sleeping':
            # 买入信号：从沉睡期切换到激活期
            execute_phase_transition('sleeping', 'active', ContextInfo, signal_result['buy_signal_details'])

        elif signal_result['has_sell_signal'] and current_phase == 'active':
            # 卖出信号：从激活期切换到沉睡期
            execute_phase_transition('active', 'sleeping', ContextInfo, signal_result['sell_signal_details'])

        # 在激活期执行价值平均策略
        if g_strategy_status['current_phase'] == 'active':
            execute_value_averaging_strategy(ContextInfo)

        # 记录当前状态
        log_message("INFO", "交易逻辑",
                   f"交易逻辑执行完成，当前阶段：{g_strategy_status['current_phase']}")

    except Exception as e:
        error_msg = f"执行交易逻辑失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "交易逻辑", error_msg)


def execute_phase_transition(from_phase: str, to_phase: str, ContextInfo, signal_details: Dict):
    """
    执行策略阶段切换

    Args:
        from_phase: 原阶段
        to_phase: 目标阶段
        ContextInfo: iQuant上下文信息对象
        signal_details: 信号详情
    """
    try:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if from_phase == 'sleeping' and to_phase == 'active':
            # 沉睡期 -> 激活期：卖出510720，买入159915
            log_message("INFO", "阶段切换", "开始从沉睡期切换到激活期")

            # 1. 卖出所有510720持仓
            position_510720 = get_current_position(SLEEPING_FUND_CODE)
            if position_510720['shares'] > 0:
                success = execute_trade_order(
                    SLEEPING_FUND_CODE, 'SELL', position_510720['shares'],
                    'SIGNAL_SELL', ContextInfo
                )
                if not success:
                    log_message("ERROR", "阶段切换", "卖出510720失败")
                    return

            # 2. 使用所有资金买入159915
            account_info = get_account_info(ContextInfo)
            available_cash = account_info['available_cash']
            current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)

            if available_cash > 0 and current_price > 0:
                shares_to_buy = int(available_cash / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
                if shares_to_buy >= MIN_TRADE_SHARES:
                    execute_trade_order(
                        ACTIVE_FUND_CODE, 'BUY', shares_to_buy,
                        'SIGNAL_BUY', ContextInfo
                    )

            # 3. 更新策略状态
            g_strategy_status['current_phase'] = 'active'
            if g_strategy_status['first_activation_time'] is None:
                g_strategy_status['first_activation_time'] = current_time

        elif from_phase == 'active' and to_phase == 'sleeping':
            # 激活期 -> 沉睡期：卖出159915，买入510720
            log_message("INFO", "阶段切换", "开始从激活期切换到沉睡期")

            # 1. 卖出所有159915持仓
            position_159915 = get_current_position(ACTIVE_FUND_CODE)
            if position_159915['shares'] > 0:
                success = execute_trade_order(
                    ACTIVE_FUND_CODE, 'SELL', position_159915['shares'],
                    'SIGNAL_SELL', ContextInfo
                )
                if not success:
                    log_message("ERROR", "阶段切换", "卖出159915失败")
                    return

            # 2. 使用所有资金买入510720
            account_info = get_account_info(ContextInfo)
            available_cash = account_info['available_cash']
            current_price = get_current_price(SLEEPING_FUND_CODE, ContextInfo)

            if available_cash > 0 and current_price > 0:
                shares_to_buy = int(available_cash / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
                if shares_to_buy >= MIN_TRADE_SHARES:
                    execute_trade_order(
                        SLEEPING_FUND_CODE, 'BUY', shares_to_buy,
                        'SIGNAL_BUY', ContextInfo
                    )

            # 3. 更新策略状态
            g_strategy_status['current_phase'] = 'sleeping'
            # 重置价值平均相关状态
            g_strategy_status['start_period_date'] = None
            g_strategy_status['start_period_price'] = None
            g_strategy_status['current_period'] = 0

        log_message("INFO", "阶段切换", f"阶段切换完成：{from_phase} -> {to_phase}")

    except Exception as e:
        error_msg = f"执行阶段切换失败：{str(e)}"
        log_message("ERROR", "阶段切换", error_msg)


def execute_value_averaging_strategy(ContextInfo):
    """
    执行价值平均策略
    在激活期根据价值平均公式调整159915持仓

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        # 检查是否为调整时机（根据投资周期）
        if not is_adjustment_time():
            return

        # 获取当前159915价格
        current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
        if current_price <= 0:
            log_message("ERROR", "价值平均", "无法获取159915当前价格")
            return

        # 计算价值平均策略
        va_result = calculate_value_averaging(current_price)

        if va_result['trade_type'] == 'BUY' and va_result['trade_shares'] > 0:
            # 需要买入
            success = execute_trade_order(
                ACTIVE_FUND_CODE, 'BUY', va_result['trade_shares'],
                'VALUE_AVERAGE', ContextInfo
            )
            if success:
                log_message("INFO", "价值平均",
                           f"价值平均买入：{va_result['trade_shares']}股，"
                           f"期数：{va_result['current_period']}")

        elif va_result['trade_type'] == 'SELL' and va_result['trade_shares'] > 0:
            # 需要卖出
            success = execute_trade_order(
                ACTIVE_FUND_CODE, 'SELL', va_result['trade_shares'],
                'VALUE_AVERAGE', ContextInfo
            )
            if success:
                log_message("INFO", "价值平均",
                           f"价值平均卖出：{va_result['trade_shares']}股，"
                           f"期数：{va_result['current_period']}")
        else:
            log_message("INFO", "价值平均",
                       f"价值平均无需调整，期数：{va_result['current_period']}")

    except Exception as e:
        error_msg = f"执行价值平均策略失败：{str(e)}"
        log_message("ERROR", "价值平均", error_msg)


def is_adjustment_time() -> bool:
    """
    判断是否为价值平均调整时机

    Returns:
        bool: 是否为调整时机
    """
    try:
        if INVESTMENT_CYCLE == "月线":
            return is_month_end_trading_day()
        elif INVESTMENT_CYCLE == "季线":
            # 季末最后一个交易日
            current_date = datetime.datetime.now()
            return current_date.month in [3, 6, 9, 12] and is_month_end_trading_day()
        elif INVESTMENT_CYCLE == "日线":
            return True  # 每日调整
        elif INVESTMENT_CYCLE == "周线":
            # 每周五调整
            return datetime.datetime.now().weekday() == 4  # 4 = Friday
        else:
            # 默认月线
            return is_month_end_trading_day()

    except Exception as e:
        log_message("ERROR", "调整时机", f"判断调整时机失败：{str(e)}")
        return False


def calculate_value_averaging(current_price: float) -> Dict:
    """
    计算价值平均策略
    基于5年历史最高点计算当前期数和目标金额

    Args:
        current_price: 当前价格

    Returns:
        dict: 价值平均计算结果
    """
    try:
        # 获取或计算价值平均起始期信息
        if g_strategy_status['start_period_date'] is None:
            # 首次计算，需要找到5年内的历史最高点
            # 注意：这里需要传入ContextInfo，但在当前函数中没有，需要重构
            start_date, start_price = get_historical_highest_price(ACTIVE_FUND_CODE, 5, None)

            # 更新策略状态
            g_strategy_status['start_period_date'] = start_date
            g_strategy_status['start_period_price'] = start_price

            log_message("INFO", "价值平均计算",
                       f"设定价值平均起始期：日期={start_date}, 价格={start_price:.4f}")

        start_date = g_strategy_status['start_period_date']
        start_price = g_strategy_status['start_period_price']

        # 计算当前期数
        current_period = calculate_current_period(start_date)

        if current_period <= 0:
            log_message("WARNING", "价值平均计算", f"当前期数计算异常：{current_period}")
            return {
                'current_period': 0,
                'target_amount': 0,
                'current_value': 0,
                'trade_amount': 0,
                'trade_shares': 0,
                'trade_type': 'HOLD'
            }

        # 计算目标金额：期数 × 每期投入金额
        target_amount = current_period * PERIOD_INVESTMENT_AMOUNT

        # 获取当前159915持仓价值
        current_position = get_current_position(ACTIVE_FUND_CODE)
        current_shares = current_position.get('shares', 0)
        current_value = current_shares * current_price

        # 计算需要调整的金额
        trade_amount = target_amount - current_value

        # 确定交易类型和股数
        if abs(trade_amount) < MIN_TRADE_SHARES * current_price:
            # 金额太小，不执行交易
            trade_type = 'HOLD'
            trade_shares = 0
            trade_amount = 0
        elif trade_amount > 0:
            # 需要买入
            trade_type = 'BUY'
            trade_shares = int(trade_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
            trade_amount = trade_shares * current_price
        else:
            # 需要卖出
            trade_type = 'SELL'
            # 卖出可以不是100的倍数，但最少100股
            trade_shares = max(MIN_TRADE_SHARES, int(abs(trade_amount) / current_price))
            trade_amount = -trade_shares * current_price  # 负数表示卖出

        # 更新当前期数到策略状态
        g_strategy_status['current_period'] = current_period

        result = {
            'current_period': current_period,
            'target_amount': target_amount,
            'current_value': current_value,
            'trade_amount': trade_amount,
            'trade_shares': trade_shares,
            'trade_type': trade_type,
            'start_date': start_date,
            'start_price': start_price
        }

        log_message("INFO", "价值平均计算",
                   f"期数={current_period}, 目标金额={target_amount:.2f}, 当前价值={current_value:.2f}, "
                   f"交易类型={trade_type}, 交易股数={trade_shares}, 交易金额={trade_amount:.2f}")

        return result

    except Exception as e:
        error_msg = f"价值平均计算失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "价值平均计算", error_msg)
        return {
            'current_period': 0,
            'target_amount': 0,
            'current_value': 0,
            'trade_amount': 0,
            'trade_shares': 0,
            'trade_type': 'HOLD'
        }


def calculate_current_period(start_date: str) -> int:
    """
    计算当前期数
    基于起始日期和投资周期计算当前是第几期

    Args:
        start_date: 起始日期字符串

    Returns:
        int: 当前期数
    """
    try:
        # 解析起始日期
        start_dt = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        current_dt = datetime.datetime.now()

        # 根据投资周期计算期数
        if INVESTMENT_CYCLE == "月线":
            # 按月计算期数
            months_diff = (current_dt.year - start_dt.year) * 12 + (current_dt.month - start_dt.month)
            return max(1, months_diff + 1)  # 至少是第1期
        elif INVESTMENT_CYCLE == "季线":
            # 按季度计算期数
            quarters_diff = ((current_dt.year - start_dt.year) * 4 +
                           (current_dt.month - 1) // 3 - (start_dt.month - 1) // 3)
            return max(1, quarters_diff + 1)
        elif INVESTMENT_CYCLE == "日线":
            # 按交易日计算期数（简化为自然日）
            days_diff = (current_dt - start_dt).days
            return max(1, days_diff + 1)
        elif INVESTMENT_CYCLE == "周线":
            # 按周计算期数
            weeks_diff = (current_dt - start_dt).days // 7
            return max(1, weeks_diff + 1)
        else:
            # 默认按月计算
            months_diff = (current_dt.year - start_dt.year) * 12 + (current_dt.month - start_dt.month)
            return max(1, months_diff + 1)

    except Exception as e:
        log_message("ERROR", "期数计算", f"计算当前期数失败：{str(e)}")
        return 1  # 出错时返回第1期


def get_current_position(stock_code: str) -> Dict:
    """
    获取指定股票的当前持仓信息

    Args:
        stock_code: 股票代码

    Returns:
        dict: 持仓信息
    """
    try:
        # 从数据库查询最新持仓记录
        cursor = g_db_connection.cursor()
        cursor.execute("""
            SELECT shares, avg_cost, market_value, current_price
            FROM position_records
            WHERE stock_code = ?
            ORDER BY record_date DESC
            LIMIT 1
        """, (stock_code,))

        result = cursor.fetchone()
        if result:
            return {
                'shares': result[0],
                'avg_cost': result[1],
                'market_value': result[2],
                'current_price': result[3]
            }
        else:
            # 没有持仓记录，返回空持仓
            return {
                'shares': 0,
                'avg_cost': 0,
                'market_value': 0,
                'current_price': 0
            }

    except Exception as e:
        log_message("ERROR", "持仓查询", f"查询持仓信息失败：{str(e)}")
        return {
            'shares': 0,
            'avg_cost': 0,
            'market_value': 0,
            'current_price': 0
        }


def execute_trade_order(stock_code: str, order_type: str, shares: int, order_reason: str, ContextInfo) -> bool:
    """
    执行交易指令
    调用iQuant交易接口执行买入或卖出操作

    Args:
        stock_code: 股票代码
        order_type: 订单类型 ('BUY' 或 'SELL')
        shares: 交易股数
        order_reason: 下单原因
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 交易是否成功
    """
    try:
        if shares <= 0:
            log_message("WARNING", "交易执行", f"交易股数无效：{shares}")
            return False

        # 获取账户信息
        account_info = get_account_info(ContextInfo)
        if not account_info:
            log_message("ERROR", "交易执行", "无法获取账户信息")
            return False

        # 记录交易指令到数据库
        order_id = record_trade_order(stock_code, order_type, shares, order_reason)

        # 根据订单类型确定操作类型
        if order_type == 'BUY':
            if stock_code == ACTIVE_FUND_CODE:
                # 买入159915，可能需要使用融资
                success = execute_buy_order(stock_code, shares, order_reason, ContextInfo, account_info)
            else:
                # 买入510720，使用普通买入
                success = execute_normal_buy(stock_code, shares, ContextInfo)
        elif order_type == 'SELL':
            # 卖出操作
            success = execute_sell_order(stock_code, shares, ContextInfo)
        else:
            log_message("ERROR", "交易执行", f"未知订单类型：{order_type}")
            return False

        # 更新交易指令状态
        update_trade_order_status(order_id, 'SUCCESS' if success else 'FAILED',
                                shares if success else 0)

        if success:
            log_message("INFO", "交易执行",
                       f"交易成功：{order_type} {stock_code} {shares}股，原因：{order_reason}")
        else:
            log_message("ERROR", "交易执行",
                       f"交易失败：{order_type} {stock_code} {shares}股，原因：{order_reason}")

        return success

    except Exception as e:
        error_msg = f"执行交易指令失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "交易执行", error_msg)
        return False


def execute_buy_order(stock_code: str, shares: int, order_reason: str, ContextInfo, account_info: Dict) -> bool:
    """
    执行买入订单，按照资金调用优先级：510720 -> 账户资金 -> 融资

    Args:
        stock_code: 股票代码
        shares: 买入股数
        order_reason: 下单原因
        ContextInfo: iQuant上下文信息对象
        account_info: 账户信息

    Returns:
        bool: 是否成功
    """
    try:
        # 获取当前价格估算所需资金
        current_price = get_current_price(stock_code, ContextInfo)
        if current_price <= 0:
            log_message("ERROR", "买入执行", f"无法获取{stock_code}当前价格")
            return False

        required_amount = shares * current_price

        # 1. 优先尝试卖出510720获取资金
        available_from_510720 = get_available_510720_amount()
        if available_from_510720 >= required_amount:
            # 卖出510720获取资金
            shares_to_sell = int(required_amount / get_current_price(SLEEPING_FUND_CODE, ContextInfo))
            if execute_sell_order(SLEEPING_FUND_CODE, shares_to_sell, ContextInfo):
                # 等待资金到账后买入目标股票
                return execute_normal_buy(stock_code, shares, ContextInfo)

        # 2. 使用账户可用资金
        available_cash = account_info.get('available_cash', 0)
        if available_cash >= required_amount:
            return execute_normal_buy(stock_code, shares, ContextInfo)

        # 3. 使用融资资金
        credit_available = account_info.get('credit_available', 0)
        if credit_available >= required_amount:
            return execute_margin_buy(stock_code, shares, ContextInfo)

        # 资金不足
        log_message("ERROR", "买入执行",
                   f"资金不足：需要{required_amount:.2f}元，510720可用{available_from_510720:.2f}元，"
                   f"现金{available_cash:.2f}元，融资额度{credit_available:.2f}元")
        return False

    except Exception as e:
        log_message("ERROR", "买入执行", f"执行买入订单失败：{str(e)}")
        return False


def execute_normal_buy(stock_code: str, shares: int, ContextInfo) -> bool:
    """
    执行普通买入操作

    Args:
        stock_code: 股票代码
        shares: 买入股数
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否成功
    """
    try:
        # 使用passorder函数执行普通买入
        # opType=23: 股票买入
        # orderType=1101: 单股、单账号、普通、股/手方式下单
        # prType=5: 最新价
        # quickTrade=1: 立即下单

        passorder(
            opType=23,           # 股票买入
            orderType=1101,      # 单股、单账号、普通、股/手方式下单
            accountid=ContextInfo.accid,  # 账户ID
            orderCode=stock_code,         # 股票代码
            prType=5,            # 最新价
            price=-1,            # 价格（最新价时无效）
            volume=shares,       # 股数
            strategyName="ValueAveraging",  # 策略名
            quickTrade=1,        # 立即下单
            userOrderId="",      # 用户订单ID
            ContextInfo=ContextInfo
        )

        return True

    except Exception as e:
        log_message("ERROR", "普通买入", f"执行普通买入失败：{str(e)}")
        return False


def execute_margin_buy(stock_code: str, shares: int, ContextInfo) -> bool:
    """
    执行融资买入操作

    Args:
        stock_code: 股票代码
        shares: 买入股数
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否成功
    """
    try:
        # 使用passorder函数执行融资买入
        # opType=27: 融资买入

        passorder(
            opType=27,           # 融资买入
            orderType=1101,      # 单股、单账号、普通、股/手方式下单
            accountid=ContextInfo.accid,  # 账户ID
            orderCode=stock_code,         # 股票代码
            prType=5,            # 最新价
            price=-1,            # 价格（最新价时无效）
            volume=shares,       # 股数
            strategyName="ValueAveraging",  # 策略名
            quickTrade=1,        # 立即下单
            userOrderId="",      # 用户订单ID
            ContextInfo=ContextInfo
        )

        return True

    except Exception as e:
        log_message("ERROR", "融资买入", f"执行融资买入失败：{str(e)}")
        return False


def execute_sell_order(stock_code: str, shares: int, ContextInfo) -> bool:
    """
    执行卖出订单

    Args:
        stock_code: 股票代码
        shares: 卖出股数
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否成功
    """
    try:
        # 使用passorder函数执行卖出
        # opType=24: 股票卖出

        passorder(
            opType=24,           # 股票卖出
            orderType=1101,      # 单股、单账号、普通、股/手方式下单
            accountid=ContextInfo.accid,  # 账户ID
            orderCode=stock_code,         # 股票代码
            prType=5,            # 最新价
            price=-1,            # 价格（最新价时无效）
            volume=shares,       # 股数
            strategyName="ValueAveraging",  # 策略名
            quickTrade=1,        # 立即下单
            userOrderId="",      # 用户订单ID
            ContextInfo=ContextInfo
        )

        return True

    except Exception as e:
        log_message("ERROR", "卖出执行", f"执行卖出失败：{str(e)}")
        return False


def record_trade_order(stock_code: str, order_type: str, shares: int, order_reason: str) -> int:
    """
    记录交易指令到数据库

    Args:
        stock_code: 股票代码
        order_type: 订单类型
        shares: 交易股数
        order_reason: 下单原因

    Returns:
        int: 订单ID
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, 'PENDING', ?)
        """, (current_time, stock_code, order_type, order_reason, shares, current_time))

        order_id = cursor.lastrowid
        g_db_connection.commit()

        return order_id

    except Exception as e:
        log_message("ERROR", "订单记录", f"记录交易指令失败：{str(e)}")
        return -1


def update_trade_order_status(order_id: int, status: str, actual_shares: int = 0,
                            actual_price: float = 0, error_message: str = None):
    """
    更新交易指令状态

    Args:
        order_id: 订单ID
        status: 订单状态
        actual_shares: 实际成交股数
        actual_price: 实际成交价格
        error_message: 错误信息
    """
    try:
        if order_id <= 0:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            UPDATE trade_orders SET
            order_status = ?, actual_shares = ?, actual_price = ?,
            error_message = ?, execution_time = ?
            WHERE id = ?
        """, (status, actual_shares, actual_price, error_message, current_time, order_id))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "订单更新", f"更新交易指令状态失败：{str(e)}")


def get_current_price(stock_code: str, ContextInfo) -> float:
    """
    获取股票当前价格

    Args:
        stock_code: 股票代码
        ContextInfo: iQuant上下文信息对象

    Returns:
        float: 当前价格
    """
    try:
        # 使用get_market_data_ex获取最新价格
        market_data = ContextInfo.get_market_data_ex(
            fields=['close'],
            stock_code=[stock_code],
            period='1min',  # 1分钟线获取最新价格
            count=1,
            dividend_type='front',
            fill_data=True
        )

        if market_data is not None and len(market_data) > 0:
            stock_data = market_data.get(stock_code)
            if stock_data is not None and len(stock_data) > 0:
                return float(stock_data['close'].iloc[-1])

        log_message("WARNING", "价格获取", f"无法获取{stock_code}的价格数据")
        return 0.0

    except Exception as e:
        log_message("ERROR", "价格获取", f"获取{stock_code}价格失败：{str(e)}")
        return 0.0


def get_available_510720_amount() -> float:
    """
    获取510720可用于卖出的金额

    Returns:
        float: 可用金额
    """
    try:
        # 从持仓记录中获取510720的持仓信息
        position = get_current_position(SLEEPING_FUND_CODE)
        shares = position.get('shares', 0)
        current_price = position.get('current_price', 0)

        if shares > 0 and current_price > 0:
            # 保留一定比例的510720持仓，不全部卖出
            available_shares = int(shares * (1 - SLEEPING_POSITION_RATIO))
            return available_shares * current_price
        else:
            return 0.0

    except Exception as e:
        log_message("ERROR", "510720资金查询", f"获取510720可用金额失败：{str(e)}")
        return 0.0


def get_account_info(ContextInfo) -> Dict:
    """
    获取账户信息
    查询账户资金、持仓、融资额度等信息

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 账户信息
    """
    try:
        # 使用get_trade_detail_data获取账户资金信息
        account_data = ContextInfo.get_trade_detail_data(
            ContextInfo.accid,
            'ACCOUNT',
            'ACCOUNT'
        )

        if account_data and len(account_data) > 0:
            account = account_data[0]

            # 获取基本资金信息
            total_assets = getattr(account, 'm_dBalance', 0)  # 总资产
            available_cash = getattr(account, 'm_dAvailable', 0)  # 可用资金

            # 获取融资信息（如果是信用账户）
            credit_limit = getattr(account, 'm_dCreditQuota', 0)  # 融资额度
            credit_used = getattr(account, 'm_dCreditUsed', 0)    # 已用融资
            credit_available = max(0, credit_limit - credit_used)  # 可用融资

            # 获取持仓信息
            positions = get_all_positions(ContextInfo)

            account_info = {
                'total_assets': total_assets,
                'available_cash': available_cash,
                'credit_limit': credit_limit,
                'credit_available': credit_available,
                'positions': positions
            }

            # 记录账户信息到数据库
            record_account_info(account_info)

            return account_info
        else:
            log_message("WARNING", "账户信息", "无法获取账户数据")
            return {
                'total_assets': 0,
                'available_cash': 0,
                'credit_limit': 0,
                'credit_available': 0,
                'positions': {}
            }

    except Exception as e:
        log_message("ERROR", "账户信息", f"获取账户信息失败：{str(e)}")
        return {
            'total_assets': 0,
            'available_cash': 0,
            'credit_limit': 0,
            'credit_available': 0,
            'positions': {}
        }


def get_all_positions(ContextInfo) -> Dict:
    """
    获取所有持仓信息

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 持仓信息字典
    """
    try:
        positions = {}

        # 获取持仓数据
        position_data = ContextInfo.get_trade_detail_data(
            ContextInfo.accid,
            'ACCOUNT',
            'POSITION'
        )

        if position_data:
            for pos in position_data:
                stock_code = getattr(pos, 'm_strInstrumentID', '')
                shares = getattr(pos, 'm_nVolume', 0)
                avg_cost = getattr(pos, 'm_dOpenCost', 0)
                current_price = getattr(pos, 'm_dSettlementPrice', 0)
                market_value = shares * current_price

                if stock_code and shares > 0:
                    positions[stock_code] = {
                        'shares': shares,
                        'avg_cost': avg_cost,
                        'current_price': current_price,
                        'market_value': market_value
                    }

                    # 记录持仓到数据库
                    record_position(stock_code, shares, avg_cost, market_value, current_price)

        return positions

    except Exception as e:
        log_message("ERROR", "持仓查询", f"获取持仓信息失败：{str(e)}")
        return {}


def record_account_info(account_info: Dict):
    """
    记录账户信息到数据库

    Args:
        account_info: 账户信息
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            INSERT INTO account_info
            (account_id, total_assets, available_cash, credit_limit, credit_available,
             update_time, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            "default",  # 账户ID
            account_info['total_assets'],
            account_info['available_cash'],
            account_info['credit_limit'],
            account_info['credit_available'],
            current_time,
            current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "账户记录", f"记录账户信息失败：{str(e)}")


def record_position(stock_code: str, shares: int, avg_cost: float,
                   market_value: float, current_price: float):
    """
    记录持仓信息到数据库

    Args:
        stock_code: 股票代码
        shares: 持仓股数
        avg_cost: 平均成本
        market_value: 市值
        current_price: 当前价格
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 如果是159915，还需要记录期数和目标价值
        period_number = None
        target_value = None
        if stock_code == ACTIVE_FUND_CODE and g_strategy_status:
            period_number = g_strategy_status.get('current_period', 0)
            if period_number > 0:
                target_value = period_number * PERIOD_INVESTMENT_AMOUNT

        cursor.execute("""
            INSERT INTO position_records
            (record_date, stock_code, shares, avg_cost, market_value, current_price,
             period_number, target_value, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            current_time, stock_code, shares, avg_cost, market_value, current_price,
            period_number, target_value, current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "持仓记录", f"记录持仓信息失败：{str(e)}")


def is_month_end_trading_day() -> bool:
    """
    判断是否为月末最后一个交易日
    用于价值平均策略的定投调整时机判断

    Returns:
        bool: 是否为月末最后交易日
    """
    try:
        current_date = datetime.datetime.now()

        # 简化判断：每月最后5个工作日内都认为是月末
        # 实际应用中可以接入交易日历API进行精确判断

        # 获取当月最后一天
        if current_date.month == 12:
            next_month = current_date.replace(year=current_date.year + 1, month=1, day=1)
        else:
            next_month = current_date.replace(month=current_date.month + 1, day=1)

        last_day_of_month = next_month - datetime.timedelta(days=1)

        # 计算距离月末的天数
        days_to_month_end = (last_day_of_month - current_date).days

        # 如果是周末，不是交易日
        if current_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
            return False

        # 距离月末5天内且是工作日，认为是月末交易日
        return days_to_month_end <= 5

    except Exception as e:
        log_message("ERROR", "月末判断", f"判断月末交易日失败：{str(e)}")
        return False


def get_historical_highest_price(stock_code: str, years: int = 5, ContextInfo=None) -> Tuple[str, float]:
    """
    获取指定年限内的历史最高价
    用于价值平均策略的起始期计算

    Args:
        stock_code: 股票代码
        years: 回溯年数

    Returns:
        tuple: (最高价日期, 最高价)
    """
    try:
        # 计算起始日期（years年前）
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=years * 365)

        start_date_str = start_date.strftime("%Y%m%d")
        end_date_str = end_date.strftime("%Y%m%d")

        # 使用get_market_data_ex获取历史数据
        # 注意：这里需要在实际运行环境中调用，回测时可能需要不同的处理
        try:
            # 获取日线数据，然后重采样为月线数据来查找最高价
            market_data = ContextInfo.get_market_data_ex(
                fields=['high', 'close'],
                stock_code=[stock_code],
                period='1d',  # 改为日线
                start_time=start_date_str,
                end_time=end_date_str,
                dividend_type='front',
                fill_data=True
            )

            if market_data is None or len(market_data) == 0:
                log_message("WARNING", "历史最高价查询", f"无法获取{stock_code}的历史数据")
                # 返回默认值
                return (start_date.strftime("%Y-%m-%d"), 10.0)

            # 获取具体股票的数据
            stock_data = market_data.get(stock_code)
            if stock_data is None or len(stock_data) == 0:
                log_message("WARNING", "历史最高价查询", f"无法获取{stock_code}的历史数据")
                # 返回默认值
                return (start_date.strftime("%Y-%m-%d"), 10.0)

            # 将日线数据重采样为月线数据
            try:
                monthly_data = resample_daily_to_period(stock_data, '1mon')
                if monthly_data is None or len(monthly_data) == 0:
                    log_message("WARNING", "历史最高价查询", f"无法重采样{stock_code}的月线数据")
                    # 返回默认值
                    return (start_date.strftime("%Y-%m-%d"), 10.0)
            except Exception as e:
                log_message("ERROR", "历史最高价查询", f"重采样失败: {str(e)}")
                # 返回默认值
                return (start_date.strftime("%Y-%m-%d"), 10.0)

            # 找到最高价及其对应的日期
            max_high_idx = monthly_data['high'].idxmax()
            max_high_price = monthly_data['high'].iloc[max_high_idx]
            max_high_date = monthly_data.index[max_high_idx]

            # 格式化日期
            if hasattr(max_high_date, 'strftime'):
                date_str = max_high_date.strftime("%Y-%m-%d")
            else:
                # 如果是字符串格式的日期，尝试转换
                try:
                    date_obj = datetime.datetime.strptime(str(max_high_date)[:8], "%Y%m%d")
                    date_str = date_obj.strftime("%Y-%m-%d")
                except:
                    date_str = start_date.strftime("%Y-%m-%d")

            log_message("INFO", "历史最高价查询",
                       f"{stock_code}在{years}年内最高价：{max_high_price:.4f}，日期：{date_str}")

            return (date_str, float(max_high_price))

        except Exception as api_error:
            log_message("WARNING", "历史最高价查询",
                       f"API调用失败：{str(api_error)}，使用默认值")
            # API调用失败时返回默认值
            return (start_date.strftime("%Y-%m-%d"), 10.0)

    except Exception as e:
        error_msg = f"获取历史最高价失败：{str(e)}"
        log_message("ERROR", "历史最高价查询", error_msg)
        # 返回默认值
        default_date = (datetime.datetime.now() - datetime.timedelta(days=years * 365)).strftime("%Y-%m-%d")
        return (default_date, 10.0)


def check_signal_filter(signal_type: str, signal_date: str) -> Tuple[bool, str]:
    """
    检查信号过滤条件
    实现FILTER功能，防止信号重复触发
    买入信号8个周期内不重复，卖出信号10个周期内不重复

    Args:
        signal_type: 信号类型 ('ENTERLONG' 或 'EXITLONG')
        signal_date: 信号日期

    Returns:
        tuple: (是否有效, 过滤原因)
    """
    try:
        # 确定过滤周期数
        if signal_type == 'ENTERLONG':
            filter_periods = BUY_SIGNAL_FILTER_PERIODS  # 8个周期
        elif signal_type == 'EXITLONG':
            filter_periods = SELL_SIGNAL_FILTER_PERIODS  # 10个周期
        else:
            return (False, f"未知信号类型：{signal_type}")

        # 查询历史信号记录
        cursor = g_db_connection.cursor()

        # 获取最近的有效信号记录，按时间倒序
        cursor.execute("""
            SELECT signal_date, signal_type FROM signal_history
            WHERE signal_type = ? AND is_valid = 1
            ORDER BY signal_date DESC
            LIMIT ?
        """, (signal_type, filter_periods))

        recent_signals = cursor.fetchall()

        if len(recent_signals) == 0:
            # 没有历史信号，当前信号有效
            return (True, None)

        # 检查是否在过滤周期内
        if len(recent_signals) >= filter_periods:
            # 已经达到过滤周期的信号数量，需要检查时间间隔
            latest_signal_date = recent_signals[0][0]

            # 计算时间间隔（这里简化处理，实际应该按季线周期计算）
            try:
                latest_time = datetime.datetime.strptime(latest_signal_date, "%Y-%m-%d %H:%M:%S")
                current_time = datetime.datetime.strptime(signal_date, "%Y-%m-%d %H:%M:%S")

                # 对于季线，一个周期大约是3个月（90天）
                days_per_period = 90
                time_diff_days = (current_time - latest_time).days
                periods_passed = time_diff_days / days_per_period

                if periods_passed < filter_periods:
                    return (False, f"距离上次{signal_type}信号仅{periods_passed:.1f}个周期，小于过滤周期{filter_periods}")
                else:
                    return (True, None)

            except ValueError as e:
                log_message("WARNING", "信号过滤", f"日期解析失败：{str(e)}")
                return (True, None)  # 解析失败时允许信号通过
        else:
            # 历史信号数量不足过滤周期，当前信号有效
            return (True, None)

    except Exception as e:
        error_msg = f"信号过滤检查失败：{str(e)}"
        log_message("ERROR", "信号过滤", error_msg)
        return (True, None)  # 出错时允许信号通过，避免错过重要信号


# ==================== 策略状态管理辅助函数 ====================

def get_strategy_phase_duration() -> int:
    """
    获取当前策略阶段持续时间（天数）

    Returns:
        int: 持续天数
    """
    try:
        if g_strategy_status is None:
            return 0

        last_check_time = g_strategy_status.get('last_check_time')
        if not last_check_time:
            return 0

        last_time = datetime.datetime.strptime(last_check_time, "%Y-%m-%d %H:%M:%S")
        current_time = datetime.datetime.now()

        return (current_time - last_time).days

    except Exception as e:
        log_message("ERROR", "状态管理", f"获取阶段持续时间失败：{str(e)}")
        return 0


def get_activation_statistics() -> Dict:
    """
    获取激活期统计信息

    Returns:
        dict: 激活期统计
    """
    try:
        stats = {
            'total_activations': 0,
            'current_activation_days': 0,
            'first_activation_time': None,
            'last_activation_time': None
        }

        if g_strategy_status is None:
            return stats

        # 从数据库查询激活期历史
        cursor = g_db_connection.cursor()

        # 查询买入信号（进入激活期）的次数
        cursor.execute("""
            SELECT COUNT(*) FROM signal_history
            WHERE signal_type = 'ENTERLONG' AND is_valid = 1
        """)
        result = cursor.fetchone()
        if result:
            stats['total_activations'] = result[0]

        # 获取首次激活时间
        stats['first_activation_time'] = g_strategy_status.get('first_activation_time')

        # 如果当前是激活期，计算持续天数
        if g_strategy_status.get('current_phase') == 'active':
            stats['current_activation_days'] = get_strategy_phase_duration()
            stats['last_activation_time'] = g_strategy_status.get('last_check_time')

        return stats

    except Exception as e:
        log_message("ERROR", "状态管理", f"获取激活期统计失败：{str(e)}")
        return {
            'total_activations': 0,
            'current_activation_days': 0,
            'first_activation_time': None,
            'last_activation_time': None
        }


def validate_strategy_state() -> bool:
    """
    验证策略状态的一致性

    Returns:
        bool: 状态是否一致
    """
    try:
        if g_strategy_status is None:
            log_message("WARNING", "状态验证", "策略状态为空")
            return False

        current_phase = g_strategy_status.get('current_phase')
        if current_phase not in ['sleeping', 'active']:
            log_message("ERROR", "状态验证", f"无效的策略阶段：{current_phase}")
            return False

        # 验证激活期状态
        if current_phase == 'active':
            start_period_date = g_strategy_status.get('start_period_date')
            if not start_period_date:
                log_message("WARNING", "状态验证", "激活期缺少起始期日期")
                return False

            current_period = g_strategy_status.get('current_period', 0)
            if current_period <= 0:
                log_message("WARNING", "状态验证", f"激活期期数异常：{current_period}")
                return False

        return True

    except Exception as e:
        log_message("ERROR", "状态验证", f"验证策略状态失败：{str(e)}")
        return False


def reset_strategy_state():
    """
    重置策略状态到初始状态
    """
    try:
        global g_strategy_status

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        g_strategy_status = {
            'id': g_strategy_status.get('id') if g_strategy_status else None,
            'current_phase': 'sleeping',
            'last_check_time': current_time,
            'first_activation_time': None,
            'start_period_date': None,
            'start_period_price': None,
            'current_period': 0,
            'created_time': g_strategy_status.get('created_time', current_time) if g_strategy_status else current_time,
            'updated_time': current_time
        }

        # 更新到数据库
        update_strategy_status(current_time)

        log_message("INFO", "状态管理", "策略状态已重置")

    except Exception as e:
        log_message("ERROR", "状态管理", f"重置策略状态失败：{str(e)}")


def get_strategy_performance_summary() -> Dict:
    """
    获取策略表现摘要

    Returns:
        dict: 表现摘要
    """
    try:
        summary = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_signals': 0,
            'valid_signals': 0,
            'filtered_signals': 0,
            'current_phase': 'unknown',
            'phase_duration_days': 0
        }

        if g_strategy_status:
            summary['current_phase'] = g_strategy_status.get('current_phase', 'unknown')
            summary['phase_duration_days'] = get_strategy_phase_duration()

        cursor = g_db_connection.cursor()

        # 统计交易次数
        cursor.execute("SELECT COUNT(*) FROM trade_orders")
        result = cursor.fetchone()
        if result:
            summary['total_trades'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_orders WHERE order_status = 'SUCCESS'")
        result = cursor.fetchone()
        if result:
            summary['successful_trades'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_orders WHERE order_status = 'FAILED'")
        result = cursor.fetchone()
        if result:
            summary['failed_trades'] = result[0]

        # 统计信号次数
        cursor.execute("SELECT COUNT(*) FROM signal_history")
        result = cursor.fetchone()
        if result:
            summary['total_signals'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM signal_history WHERE is_valid = 1")
        result = cursor.fetchone()
        if result:
            summary['valid_signals'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM signal_history WHERE is_valid = 0")
        result = cursor.fetchone()
        if result:
            summary['filtered_signals'] = result[0]

        return summary

    except Exception as e:
        log_message("ERROR", "状态管理", f"获取策略表现摘要失败：{str(e)}")
        return {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_signals': 0,
            'valid_signals': 0,
            'filtered_signals': 0,
            'current_phase': 'unknown',
            'phase_duration_days': 0
        }

